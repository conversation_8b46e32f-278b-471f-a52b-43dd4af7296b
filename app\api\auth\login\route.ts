import { NextRequest } from 'next/server';
import { z } from 'zod';
import {
  successResponse,
  errorResponse,
  validationErrorResponse,
  withErrorHandling,
  generateRequestId,
  getClientIP,
  parseUserAgent
} from '@/lib/api/utils';
import { ApiErrorCode, HttpStatus, LoginRequest, AuthResponse } from '@/lib/api/types';
import { AuthService } from '@/lib/database/auth';
// Validation schema
const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional().default(false)
});
// Production authentication using database service

async function loginHandler(request: NextRequest) {
  const requestId = generateRequestId();
  try {
    // Parse request body
    const body = await request.json();
    // Validate input
    const validation = loginSchema.safeParse(body);
    if (!validation.success) {
      const errors = validation.error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
        code: 'INVALID_FORMAT'
      }));
      return validationErrorResponse(errors, requestId);
    }
    const { email, password, rememberMe } = validation.data;

    // Authenticate user using production database service
    try {
      const authResult = await AuthService.login({ email, password });
    // Log successful login
    const clientIP = getClientIP(request);
    const userAgent = request.headers.get('user-agent') || '';
    const { browser, os } = parseUserAgent(userAgent);

    console.log('Successful login:', {
      userId: authResult.user.id,
      email: authResult.user.email,
      ip: clientIP,
      userAgent,
      browser,
      os,
      timestamp: new Date().toISOString()
    });

    // Prepare response data
    const responseData: AuthResponse = {
      user: authResult.user,
      token: authResult.token,
      expiresAt: new Date(Date.now() + (rememberMe ? 30 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000)).toISOString()
    };

      // Create response with secure headers
      const response = successResponse(responseData, undefined, HttpStatus.OK, requestId);
      return response;
    } catch (authError) {
      // Handle authentication-specific errors
      const errorMessage = authError instanceof Error ? authError.message : 'Authentication failed';
      return errorResponse(
        ApiErrorCode.UNAUTHORIZED,
        errorMessage,
        HttpStatus.UNAUTHORIZED,
        undefined,
        requestId
      );
    }
  } catch (error) {
    console.error('Login error:', error);
    return errorResponse(
      ApiErrorCode.INTERNAL_SERVER_ERROR,
      'Login failed',
      HttpStatus.INTERNAL_SERVER_ERROR,
      undefined,
      requestId
    );
  }
}
// Export handlers
export const POST = withErrorHandling(loginHandler);
