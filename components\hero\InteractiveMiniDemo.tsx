'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Play,
  X,
  ChevronRight,
  ChevronLeft,
  Code,
  Zap,
  Users,
  CheckCircle,
  Sparkles,
  Rocket,
  Brain,
  Clock,
  Volume2,
  VolumeX,
  Keyboard,
  Trophy,
  Star,
  Target,
  BookOpen,
  Shield
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { AdvancedCodeEditor } from './AdvancedCodeEditor';
import { SolidityCompiler } from './SolidityCompiler';
import { GamificationSystem } from './GamificationSystem';
import { StrategicAccountCreation } from './StrategicAccountCreation';
import { XPNotificationManager, useXPNotifications } from '@/components/xp/XPNotification';
import { MicroAchievementManager, useMicroAchievements } from '@/components/gamification/MicroAchievements';
import { AchievementNotificationManager, useAchievementNotifications } from '@/components/gamification/AchievementNotificationManager';
import { StreakTracker, useStreakManager } from '@/components/gamification/StreakTracker';
import { LevelUpManager, useLevelUp } from '@/components/xp/LevelUpCelebration';
import {
  EnhancedDemoStep,
  EnhancedInteractiveMiniDemoProps,
  DemoState,
  LearningObjective,
  KeyboardShortcut,
  VoiceOverConfig,
  ProgressData,
  Achievement
} from './types/DemoTypes';

interface LearningObjective {
  id: string;
  title: string;
  description: string;
  completed: boolean;
}

interface DemoStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  content: React.ReactNode;
  duration: number;
  autoAdvance?: boolean;
  learningObjectives: LearningObjective[];
  xpReward: number;
  voiceOverText?: string;
}

interface InteractiveMiniDemoProps {
  className?: string;
  triggerText?: string;
  autoStart?: boolean;
  trackAnalytics?: boolean;
  respectReducedMotion?: boolean;
  enableVoiceOver?: boolean;
  enableTooltips?: boolean;
  enableTimer?: boolean;
  totalDuration?: number;
}

/**
 * Interactive Mini-Demo Component
 * 
 * 30-second interactive preview showcasing core platform features with guided 3-step experience.
 * Features modal overlay with embedded code preview, AI assistance demo, and collaboration features.
 * 
 * @component
 * @example
 * ```tsx
 * <InteractiveMiniDemo 
 *   triggerText="Try it now"
 *   autoStart={false}
 *   trackAnalytics={true}
 *   respectReducedMotion={true}
 * />
 * ```
 */
export function InteractiveMiniDemo({
  className = '',
  triggerText = 'Try Interactive Demo',
  autoStart = false,
  trackAnalytics = true,
  respectReducedMotion = true,
  enableVoiceOver = true,
  enableTooltips = true,
  enableTimer = true,
  totalDuration = 180000 // 3 minutes
}: InteractiveMiniDemoProps) {
  const [isOpen, setIsOpen] = useState(autoStart);
  const [currentStep, setCurrentStep] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const [startTime, setStartTime] = useState<Date | null>(null);
  const [timeRemaining, setTimeRemaining] = useState(totalDuration);
  const [voiceOverEnabled, setVoiceOverEnabled] = useState(enableVoiceOver);
  const [showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false);
  const [totalXP, setTotalXP] = useState(0);
  const [achievements, setAchievements] = useState<string[]>([]);
  const [unlockedAchievements, setUnlockedAchievements] = useState<any[]>([]);
  const [showAccountCreation, setShowAccountCreation] = useState(false);
  const [userAccount, setUserAccount] = useState<{ method?: string; email?: string } | null>(null);
  const [currentLevel, setCurrentLevel] = useState(1);
  const [sessionXP, setSessionXP] = useState(0);
  const [streakData, setStreakData] = useState({
    currentStreak: 1,
    longestStreak: 1,
    lastActivity: new Date(),
    streakGoal: 7,
    todayCompleted: false,
    streakHistory: [new Date()],
    milestones: []
  });

  // Gamification hooks
  const { triggerXPGain, triggerLessonXP, triggerBonusXP, triggerAchievementXP } = useXPNotifications();
  const { triggerMicroAchievement } = useMicroAchievements();
  const { triggerAchievementByRarity } = useAchievementNotifications();
  const { triggerLevelUp } = useLevelUp();
  const { streakData: managedStreakData, extendStreak } = useStreakManager();

  // Achievement definitions
  const achievementDefinitions = [
    {
      id: 'first-step',
      title: 'Getting Started',
      description: 'Complete your first demo step',
      icon: Target,
      xpReward: 25,
      unlocked: achievements.includes('first-step'),
      rarity: 'common' as const
    },
    {
      id: 'halfway',
      title: 'Halfway Hero',
      description: 'Reach the halfway point',
      icon: Star,
      xpReward: 50,
      unlocked: achievements.includes('halfway'),
      rarity: 'rare' as const
    },
    {
      id: 'speed-demon',
      title: 'Speed Demon',
      description: 'Complete demo in under 2 minutes',
      icon: Zap,
      xpReward: 100,
      unlocked: achievements.includes('speed-demon'),
      rarity: 'epic' as const
    },
    {
      id: 'demo-complete',
      title: 'Demo Master',
      description: 'Complete the entire demo',
      icon: Trophy,
      xpReward: 200,
      unlocked: achievements.includes('demo-complete'),
      rarity: 'legendary' as const
    },
    {
      id: 'code-explorer',
      title: 'Code Explorer',
      description: 'Interact with the code editor',
      icon: Code,
      xpReward: 30,
      unlocked: achievements.includes('code-explorer'),
      rarity: 'common' as const
    },
    {
      id: 'compiler-user',
      title: 'Compiler Expert',
      description: 'Successfully compile a contract',
      icon: CheckCircle,
      xpReward: 75,
      unlocked: achievements.includes('compiler-user'),
      rarity: 'rare' as const
    }
  ];

  // Progress data for gamification
  const progressData = {
    currentStep,
    completedSteps,
    totalSteps: demoSteps.length,
    totalXP,
    achievements: achievementDefinitions,
    startTime: startTime || new Date(),
    lastActiveTime: new Date(),
    completionPercentage: progress,
    estimatedTimeRemaining: timeRemaining,
    streak: 1
  };
  
  const modalRef = useRef<HTMLDivElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);

  // Check for reduced motion preference
  const prefersReducedMotion = typeof window !== 'undefined' 
    ? window.matchMedia('(prefers-reduced-motion: reduce)').matches 
    : false;

  const shouldAnimate = respectReducedMotion ? !prefersReducedMotion : true;

  // Enhanced 5-step demo configuration
  const demoSteps: DemoStep[] = [
    {
      id: 'introduction',
      title: 'Welcome to Solidity',
      description: 'Learn the basics of smart contract development',
      icon: BookOpen,
      duration: 30000,
      autoAdvance: true,
      xpReward: 25,
      voiceOverText: 'Welcome to your Solidity learning journey! In the next 3 minutes, you\'ll write, compile, and deploy your first smart contract.',
      learningObjectives: [
        { id: 'understand-blockchain', title: 'Understand blockchain basics', description: 'Learn what smart contracts are', completed: false },
        { id: 'solidity-intro', title: 'Introduction to Solidity', description: 'Understand Solidity syntax', completed: false }
      ],
      content: <IntroductionDemo />
    },
    {
      id: 'write-code',
      title: 'Write Smart Contract',
      description: 'Create your first Solidity contract with guided assistance',
      icon: Code,
      duration: 45000,
      autoAdvance: true,
      xpReward: 50,
      voiceOverText: 'Now let\'s write your first smart contract. Watch as we create a simple Hello World contract step by step.',
      learningObjectives: [
        { id: 'contract-structure', title: 'Contract structure', description: 'Learn contract anatomy', completed: false },
        { id: 'variables-functions', title: 'Variables and functions', description: 'Understand state variables and functions', completed: false }
      ],
      content: <CodeEditorDemo />
    },
    {
      id: 'ai-assistance',
      title: 'AI-Powered Learning',
      description: 'Experience intelligent code suggestions and explanations',
      icon: Brain,
      duration: 40000,
      autoAdvance: true,
      xpReward: 75,
      voiceOverText: 'Our AI assistant will help you understand every line of code and suggest improvements.',
      learningObjectives: [
        { id: 'ai-suggestions', title: 'AI code suggestions', description: 'Learn from AI recommendations', completed: false },
        { id: 'best-practices', title: 'Best practices', description: 'Understand Solidity best practices', completed: false }
      ],
      content: <AIAssistanceDemo />
    },
    {
      id: 'compile-test',
      title: 'Compile & Test',
      description: 'Compile your contract and run security analysis',
      icon: Shield,
      duration: 35000,
      autoAdvance: true,
      xpReward: 100,
      voiceOverText: 'Time to compile your contract and check for any issues. Our compiler will provide detailed feedback.',
      learningObjectives: [
        { id: 'compilation', title: 'Contract compilation', description: 'Understand the compilation process', completed: false },
        { id: 'security-analysis', title: 'Security analysis', description: 'Learn about security best practices', completed: false }
      ],
      content: <CompilationDemo />
    },
    {
      id: 'deploy-share',
      title: 'Deploy & Collaborate',
      description: 'Deploy to testnet and share with the community',
      icon: Rocket,
      duration: 30000,
      autoAdvance: false,
      xpReward: 150,
      voiceOverText: 'Finally, let\'s deploy your contract to a test network and see how you can collaborate with other developers.',
      learningObjectives: [
        { id: 'deployment', title: 'Contract deployment', description: 'Deploy to testnet', completed: false },
        { id: 'collaboration', title: 'Developer collaboration', description: 'Share and collaborate on contracts', completed: false }
      ],
      content: <CollaborationDemo />
    }
  ];

  // Timer countdown logic
  useEffect(() => {
    if (!isPlaying || !isOpen || !enableTimer) return;

    const interval = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1000) {
          setIsPlaying(false);
          return 0;
        }
        return prev - 1000;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [isPlaying, isOpen, enableTimer]);

  // Auto-advance logic
  useEffect(() => {
    if (!isPlaying || !isOpen) return;

    const currentStepData = demoSteps[currentStep];
    if (!currentStepData.autoAdvance) return;

    const timer = setTimeout(() => {
      handleNextStep();
    }, currentStepData.duration);

    return () => clearTimeout(timer);
  }, [currentStep, isPlaying, isOpen]);

  // Voice-over functionality
  const playVoiceOver = useCallback((text: string) => {
    if (!voiceOverEnabled || !text || typeof window === 'undefined') return;

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = 0.9;
    utterance.pitch = 1;
    utterance.volume = 0.8;

    window.speechSynthesis.speak(utterance);
  }, [voiceOverEnabled]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'Escape':
          handleClose();
          break;
        case 'ArrowRight':
          e.preventDefault();
          handleNextStep();
          break;
        case 'ArrowLeft':
          e.preventDefault();
          handlePrevStep();
          break;
        case ' ':
          e.preventDefault();
          setIsPlaying(!isPlaying);
          break;
        case '?':
          e.preventDefault();
          setShowKeyboardShortcuts(!showKeyboardShortcuts);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, isPlaying, showKeyboardShortcuts]);

  // Analytics tracking
  useEffect(() => {
    if (trackAnalytics && isOpen && !startTime) {
      setStartTime(new Date());
      // Track demo start event
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('event', 'demo_started', {
          event_category: 'engagement',
          event_label: 'hero_mini_demo'
        });
      }
    }
  }, [isOpen, startTime, trackAnalytics]);

  const handleOpen = () => {
    setIsOpen(true);
    setIsPlaying(true);
    setCurrentStep(0);
    setCompletedSteps(new Set());
  };

  const handleClose = () => {
    setIsOpen(false);
    setIsPlaying(false);
    setCurrentStep(0);
    
    // Track completion analytics
    if (trackAnalytics && startTime) {
      const duration = new Date().getTime() - startTime.getTime();
      const completionRate = (completedSteps.size / demoSteps.length) * 100;
      
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('event', 'demo_completed', {
          event_category: 'engagement',
          event_label: 'hero_mini_demo',
          value: Math.round(duration / 1000),
          custom_parameter_1: completionRate
        });
      }
    }
  };

  const handleNextStep = () => {
    const currentStepData = demoSteps[currentStep];

    // Mark step as completed and award XP
    setCompletedSteps(prev => new Set([...prev, currentStep]));
    const xpGained = currentStepData.xpReward;
    setTotalXP(prev => prev + xpGained);
    setSessionXP(prev => prev + xpGained);

    // Trigger XP notification with position
    const buttonElement = document.querySelector('[data-step-button]');
    const position = buttonElement ? {
      x: buttonElement.getBoundingClientRect().left + buttonElement.getBoundingClientRect().width / 2,
      y: buttonElement.getBoundingClientRect().top
    } : undefined;

    triggerLessonXP(xpGained, currentStepData.title, position);

    // Trigger micro achievements
    if (currentStep === 0) {
      triggerMicroAchievement('first-step');
    }

    // Check for speed bonus
    if (startTime && (new Date().getTime() - startTime.getTime()) < 30000) {
      triggerMicroAchievement('quick-learner');
      triggerBonusXP(25, 'Speed bonus!', 2, position);
    }

    // Play voice-over for next step
    if (currentStep < demoSteps.length - 1) {
      const nextStep = demoSteps[currentStep + 1];
      if (nextStep.voiceOverText) {
        setTimeout(() => playVoiceOver(nextStep.voiceOverText!), 500);
      }
    }

    // Check for achievements
    checkAchievements(currentStep);

    // Check for level up
    const newTotalXP = totalXP + xpGained;
    const newLevel = Math.floor(newTotalXP / 1000) + 1;
    if (newLevel > currentLevel) {
      setCurrentLevel(newLevel);
      triggerLevelUp({
        newLevel,
        previousLevel: currentLevel,
        xpGained: xpGained,
        totalXP: newTotalXP,
        rewards: {
          xp: 100,
          badges: [`Level ${newLevel} Badge`],
          titles: [`Level ${newLevel} Achiever`]
        },
        levelInfo: {
          title: `Level ${newLevel}`,
          description: `You've reached level ${newLevel}!`,
          color: 'bg-gradient-to-br from-yellow-400 to-orange-500'
        }
      });
    }

    // Track analytics
    if (trackAnalytics && typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'demo_step_completed', {
        event_category: 'engagement',
        event_label: currentStepData.id,
        value: currentStepData.xpReward
      });
    }

    if (currentStep < demoSteps.length - 1) {
      setCurrentStep(prev => prev + 1);
    } else {
      setIsPlaying(false);
      handleDemoCompletion();
    }
  };

  const checkAchievements = (stepIndex: number) => {
    const newAchievements: string[] = [];

    // First step completion
    if (stepIndex === 0 && !achievements.includes('first-step')) {
      newAchievements.push('first-step');
      const achievement = achievementDefinitions.find(a => a.id === 'first-step');
      if (achievement) {
        triggerAchievementXP(achievement.xpReward, achievement.title);
        triggerAchievementByRarity(achievement as any);
      }
    }

    // Halfway point
    if (stepIndex === Math.floor(demoSteps.length / 2) && !achievements.includes('halfway')) {
      newAchievements.push('halfway');
      const achievement = achievementDefinitions.find(a => a.id === 'halfway');
      if (achievement) {
        triggerAchievementXP(achievement.xpReward, achievement.title);
        triggerAchievementByRarity(achievement as any);
      }
    }

    // Speed demon (completing steps quickly)
    if (startTime && (new Date().getTime() - startTime.getTime()) < 120000 && stepIndex === 2) {
      newAchievements.push('speed-demon');
      const achievement = achievementDefinitions.find(a => a.id === 'speed-demon');
      if (achievement) {
        triggerAchievementXP(achievement.xpReward, achievement.title);
        triggerAchievementByRarity(achievement as any);
      }
    }

    if (newAchievements.length > 0) {
      setAchievements(prev => [...prev, ...newAchievements]);
    }
  };

  const handleDemoCompletion = () => {
    // Award completion achievement
    if (!achievements.includes('demo-complete')) {
      setAchievements(prev => [...prev, 'demo-complete']);
      const completionXP = 200;
      setTotalXP(prev => prev + completionXP);
      setSessionXP(prev => prev + completionXP);

      // Trigger completion achievement
      const achievement = achievementDefinitions.find(a => a.id === 'demo-complete');
      if (achievement) {
        triggerAchievementXP(achievement.xpReward, achievement.title);
        triggerAchievementByRarity(achievement as any);
      }

      // Trigger completion micro achievement
      triggerMicroAchievement('perfectionist');

      // Extend streak
      extendStreak();
    }

    // Track completion analytics
    if (trackAnalytics && startTime && typeof window !== 'undefined' && (window as any).gtag) {
      const completionTime = new Date().getTime() - startTime.getTime();
      (window as any).gtag('event', 'demo_completed', {
        event_category: 'engagement',
        event_label: 'full_completion',
        value: Math.round(completionTime / 1000),
        custom_parameter_1: totalXP + 200
      });
    }

    // Show account creation prompt if not already created
    if (!userAccount) {
      setShowAccountCreation(true);
    }
  };

  const handleAccountCreated = (method: string, email?: string) => {
    setUserAccount({ method, email });
    setShowAccountCreation(false);

    // Award account creation achievement
    if (!achievements.includes('account-created')) {
      setAchievements(prev => [...prev, 'account-created']);
      setTotalXP(prev => prev + 100);
    }

    // Track analytics
    if (trackAnalytics && typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'account_created', {
        event_category: 'conversion',
        event_label: method,
        value: 1
      });
    }
  };

  const handleGuestContinue = (email?: string) => {
    setShowAccountCreation(false);

    // Track guest continuation
    if (trackAnalytics && typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'guest_continue', {
        event_category: 'engagement',
        event_label: email ? 'with_email' : 'without_email'
      });
    }
  };

  const handleGuestContinue = (email?: string) => {
    setShowAccountCreation(false);

    // Track guest continuation
    if (trackAnalytics && typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'guest_continue', {
        event_category: 'engagement',
        event_label: email ? 'with_email' : 'without_email'
      });
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleStepClick = (stepIndex: number) => {
    setCurrentStep(stepIndex);
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;
      
      switch (e.key) {
        case 'Escape':
          handleClose();
          break;
        case 'ArrowRight':
          handleNextStep();
          break;
        case 'ArrowLeft':
          handlePrevStep();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, currentStep]);

  const currentStepData = demoSteps[currentStep];
  const progress = ((currentStep + 1) / demoSteps.length) * 100;

  return (
    <>
      {/* Trigger Button */}
      <motion.button
        onClick={handleOpen}
        className={cn(
          'inline-flex items-center space-x-2 px-6 py-3 rounded-lg font-medium',
          'glass border border-white/20 text-white backdrop-blur-md bg-white/10',
          'hover:bg-white/20 hover:border-white/30 hover:shadow-lg',
          'focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-transparent',
          'transition-all duration-300 group',
          className
        )}
        whileHover={shouldAnimate ? { scale: 1.02 } : {}}
        whileTap={shouldAnimate ? { scale: 0.98 } : {}}
        aria-label="Open interactive demo"
      >
        <Play className="w-4 h-4 group-hover:scale-110 transition-transform duration-300" />
        <span>{triggerText}</span>
        <Sparkles className="w-4 h-4 text-purple-400" />
      </motion.button>

      {/* Modal Overlay */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={handleClose}
          >
            <motion.div
              ref={modalRef}
              className="glass max-w-4xl w-full max-h-[90vh] rounded-xl border border-white/20 backdrop-blur-md bg-white/10 overflow-hidden shadow-2xl"
              initial={shouldAnimate ? { scale: 0.8, opacity: 0 } : {}}
              animate={{ scale: 1, opacity: 1 }}
              exit={shouldAnimate ? { scale: 0.8, opacity: 0 } : {}}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
              onClick={(e) => e.stopPropagation()}
              role="dialog"
              aria-modal="true"
              aria-labelledby="demo-title"
              aria-describedby="demo-description"
            >
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-white/10">
                <div className="flex-1">
                  <div className="flex items-center space-x-4">
                    <div>
                      <h2 id="demo-title" className="text-xl font-semibold text-white">
                        Interactive Platform Demo
                      </h2>
                      <p id="demo-description" className="text-sm text-gray-300 mt-1">
                        Follow along in 3 minutes - Learn by doing
                      </p>
                    </div>

                    {/* Timer Display */}
                    {enableTimer && (
                      <div className="flex items-center space-x-2 px-3 py-1 bg-blue-500/20 rounded-full">
                        <Clock className="w-4 h-4 text-blue-400" />
                        <span className="text-sm font-mono text-blue-300">
                          {Math.floor(timeRemaining / 60000)}:{String(Math.floor((timeRemaining % 60000) / 1000)).padStart(2, '0')}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  {/* Voice-over toggle */}
                  {enableVoiceOver && (
                    <button
                      onClick={() => setVoiceOverEnabled(!voiceOverEnabled)}
                      className={cn(
                        'p-2 rounded-lg transition-colors',
                        voiceOverEnabled
                          ? 'text-blue-400 bg-blue-500/20 hover:bg-blue-500/30'
                          : 'text-gray-400 hover:text-white hover:bg-white/10'
                      )}
                      aria-label={voiceOverEnabled ? "Disable voice-over" : "Enable voice-over"}
                    >
                      {voiceOverEnabled ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
                    </button>
                  )}

                  {/* Keyboard shortcuts */}
                  <button
                    onClick={() => setShowKeyboardShortcuts(!showKeyboardShortcuts)}
                    className="p-2 text-gray-400 hover:text-white transition-colors rounded-lg hover:bg-white/10"
                    aria-label="Show keyboard shortcuts"
                  >
                    <Keyboard className="w-4 h-4" />
                  </button>

                  <button
                    onClick={handleClose}
                    className="p-2 text-gray-400 hover:text-white transition-colors rounded-lg hover:bg-white/10"
                    aria-label="Close demo"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {/* Enhanced Progress Bar */}
              <div className="px-6 py-3 bg-gray-900/50">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-4">
                    <span className="text-sm font-medium text-white">
                      Step {currentStep + 1} of {demoSteps.length}
                    </span>
                    <div className="flex items-center space-x-2 px-2 py-1 bg-yellow-500/20 rounded-full">
                      <Trophy className="w-3 h-3 text-yellow-400" />
                      <span className="text-xs font-bold text-yellow-300">{totalXP} XP</span>
                    </div>
                    {achievements.length > 0 && (
                      <div className="flex items-center space-x-1">
                        <Star className="w-3 h-3 text-purple-400" />
                        <span className="text-xs text-purple-300">{achievements.length} achievements</span>
                      </div>
                    )}
                  </div>
                  <span className="text-xs text-gray-400">
                    {Math.round(progress)}% Complete
                  </span>
                </div>

                <div className="w-full h-3 bg-white/10 rounded-full overflow-hidden mb-2">
                  <motion.div
                    ref={progressRef}
                    className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full relative"
                    initial={{ width: '0%' }}
                    animate={{ width: `${progress}%` }}
                    transition={{ duration: 0.5, ease: "easeOut" }}
                  >
                    {/* Progress glow effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full blur-sm opacity-50" />
                  </motion.div>
                </div>

                {/* Learning Objectives */}
                <div className="mt-3">
                  <h4 className="text-xs font-medium text-gray-300 mb-2">Learning Objectives:</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {currentStepData.learningObjectives.map((objective, index) => (
                      <div key={objective.id} className="flex items-center space-x-2">
                        <div className={cn(
                          'w-4 h-4 rounded-full flex items-center justify-center',
                          objective.completed
                            ? 'bg-green-500/20 border border-green-500/30'
                            : 'bg-gray-500/20 border border-gray-500/30'
                        )}>
                          {objective.completed && <CheckCircle className="w-3 h-3 text-green-400" />}
                          {!objective.completed && <Target className="w-3 h-3 text-gray-400" />}
                        </div>
                        <span className={cn(
                          'text-xs',
                          objective.completed ? 'text-green-300' : 'text-gray-400'
                        )}>
                          {objective.title}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Step Navigation */}
              <div className="px-6 py-2">
                <div className="flex items-center space-x-2">
                  {demoSteps.map((step, index) => (
                    <button
                      key={step.id}
                      onClick={() => handleStepClick(index)}
                      className={cn(
                        'flex items-center space-x-2 px-3 py-2 rounded-lg text-sm transition-all duration-300',
                        index === currentStep
                          ? 'bg-purple-500/20 text-purple-300 border border-purple-500/30'
                          : completedSteps.has(index)
                          ? 'bg-green-500/20 text-green-300 border border-green-500/30'
                          : 'bg-white/5 text-gray-400 border border-white/10 hover:bg-white/10'
                      )}
                      aria-label={`Go to step ${index + 1}: ${step.title}`}
                    >
                      <step.icon className="w-4 h-4" />
                      <span className="hidden sm:inline">{step.title}</span>
                      {completedSteps.has(index) && (
                        <CheckCircle className="w-3 h-3 text-green-400" />
                      )}
                    </button>
                  ))}
                </div>
              </div>

              {/* Content Area */}
              <div className="flex-1 overflow-hidden">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 p-6 h-full">
                  {/* Demo Content */}
                  <div className="lg:col-span-2 space-y-4">
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-2">
                        {currentStepData.title}
                      </h3>
                      <p className="text-gray-300">
                        {currentStepData.description}
                      </p>
                    </div>

                    <AnimatePresence mode="wait">
                      <motion.div
                        key={currentStep}
                        initial={shouldAnimate ? { opacity: 0, x: 20 } : {}}
                        animate={{ opacity: 1, x: 0 }}
                        exit={shouldAnimate ? { opacity: 0, x: -20 } : {}}
                        transition={{ duration: 0.3 }}
                        className="h-full"
                      >
                        {currentStepData.content}
                      </motion.div>
                    </AnimatePresence>
                  </div>

                  {/* Gamification Sidebar */}
                  <div className="lg:col-span-1 overflow-y-auto">
                    <GamificationSystem
                      progress={progressData}
                      onAchievementUnlocked={(achievement) => {
                        console.log('Achievement unlocked:', achievement);
                        setUnlockedAchievements(prev => [...prev, achievement]);
                      }}
                      onXPEarned={(amount, reason) => {
                        console.log(`Earned ${amount} XP for ${reason}`);
                      }}
                      showLeaderboard={true}
                    />
                  </div>
                </div>
              </div>

              {/* Keyboard Shortcuts Overlay */}
              {showKeyboardShortcuts && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute top-16 right-6 z-10 bg-gray-900/95 backdrop-blur-md border border-white/20 rounded-lg p-4 min-w-[250px]"
                >
                  <h4 className="text-sm font-semibold text-white mb-3">Keyboard Shortcuts</h4>
                  <div className="space-y-2 text-xs">
                    <div className="flex justify-between">
                      <span className="text-gray-300">Play/Pause</span>
                      <kbd className="px-2 py-1 bg-white/10 rounded text-gray-300">Space</kbd>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">Next Step</span>
                      <kbd className="px-2 py-1 bg-white/10 rounded text-gray-300">→</kbd>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">Previous Step</span>
                      <kbd className="px-2 py-1 bg-white/10 rounded text-gray-300">←</kbd>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">Close Demo</span>
                      <kbd className="px-2 py-1 bg-white/10 rounded text-gray-300">Esc</kbd>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">Show Shortcuts</span>
                      <kbd className="px-2 py-1 bg-white/10 rounded text-gray-300">?</kbd>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Footer Controls */}
              <div className="flex items-center justify-between p-6 border-t border-white/10">
                <button
                  onClick={handlePrevStep}
                  disabled={currentStep === 0}
                  className="flex items-center space-x-2 px-4 py-2 rounded-lg bg-white/10 hover:bg-white/20 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  aria-label="Previous step"
                >
                  <ChevronLeft className="w-4 h-4" />
                  <span>Previous</span>
                </button>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setIsPlaying(!isPlaying)}
                    className="p-2 rounded-lg bg-purple-500/20 hover:bg-purple-500/30 text-purple-300 transition-colors"
                    aria-label={isPlaying ? "Pause demo" : "Play demo"}
                  >
                    {isPlaying ? <Zap className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                  </button>
                </div>

                <button
                  onClick={handleNextStep}
                  disabled={currentStep === demoSteps.length - 1 && !isPlaying}
                  className="flex items-center space-x-2 px-4 py-2 rounded-lg bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  aria-label="Next step"
                  data-step-button
                >
                  <span>
                    {currentStep === demoSteps.length - 1 ? 'Finish' : 'Next'}
                  </span>
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Strategic Account Creation */}
      <StrategicAccountCreation
        currentStep={currentStep}
        totalSteps={demoSteps.length}
        timeElapsed={startTime ? new Date().getTime() - startTime.getTime() : 0}
        totalXP={totalXP}
        achievements={achievements.length}
        onAccountCreated={handleAccountCreated}
        onGuestContinue={handleGuestContinue}
        onDismiss={() => setShowAccountCreation(false)}
      />

      {/* Gamification Notification Systems */}
      <XPNotificationManager
        enableSound={true}
        maxNotifications={5}
      />

      <MicroAchievementManager
        onAchievementUnlocked={(achievement) => {
          console.log('Micro achievement unlocked:', achievement);
        }}
      />

      <AchievementNotificationManager
        maxToasts={3}
        toastDuration={5000}
        onShare={(achievement, platform) => {
          console.log(`Sharing achievement ${achievement.title} on ${platform}`);
        }}
      />

      <LevelUpManager />
    </>
  );
}

/**
 * Introduction Demo Component
 */
function IntroductionDemo() {
  return (
    <div className="space-y-6">
      <div className="text-center">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.5, type: "spring" }}
          className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center"
        >
          <Sparkles className="w-10 h-10 text-white" />
        </motion.div>
        <h3 className="text-2xl font-bold text-white mb-2">Welcome to Solidity!</h3>
        <p className="text-gray-300 max-w-md mx-auto">
          Smart contracts are self-executing contracts with terms directly written into code.
          They run on blockchain networks like Ethereum.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 text-center">
          <Shield className="w-8 h-8 text-blue-400 mx-auto mb-2" />
          <h4 className="text-blue-300 font-medium mb-1">Secure</h4>
          <p className="text-xs text-gray-400">Immutable and tamper-proof</p>
        </div>

        <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4 text-center">
          <Zap className="w-8 h-8 text-green-400 mx-auto mb-2" />
          <h4 className="text-green-300 font-medium mb-1">Automated</h4>
          <p className="text-xs text-gray-400">Execute without intermediaries</p>
        </div>

        <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-4 text-center">
          <Users className="w-8 h-8 text-purple-400 mx-auto mb-2" />
          <h4 className="text-purple-300 font-medium mb-1">Decentralized</h4>
          <p className="text-xs text-gray-400">No single point of failure</p>
        </div>
      </div>

      <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <BookOpen className="w-6 h-6 text-yellow-400 mt-1" />
          <div>
            <h4 className="text-yellow-300 font-medium mb-1">What you'll learn:</h4>
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• Write your first smart contract</li>
              <li>• Understand Solidity syntax and structure</li>
              <li>• Compile and deploy to testnet</li>
              <li>• Use AI assistance for better code</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Code Editor Demo Component
 */
function CodeEditorDemo() {
  const [code, setCode] = useState('');
  const [isTyping, setIsTyping] = useState(true);
  const [showEditor, setShowEditor] = useState(false);

  const finalCode = `pragma solidity ^0.8.0;

contract HelloWorld {
    string public message;

    constructor() {
        message = "Hello, Solidity!";
    }

    function setMessage(string memory _message) public {
        message = _message;
    }
}`;

  const annotations = [
    {
      line: 1,
      type: 'info' as const,
      title: 'Pragma Directive',
      content: 'Specifies the Solidity compiler version. ^0.8.0 means compatible with 0.8.0 and above.',
      link: 'https://docs.soliditylang.org/en/latest/layout-of-source-files.html#pragma'
    },
    {
      line: 3,
      type: 'info' as const,
      title: 'Contract Declaration',
      content: 'Defines a new smart contract named HelloWorld. Similar to a class in other languages.',
    },
    {
      line: 4,
      type: 'tip' as const,
      title: 'State Variable',
      content: 'Public state variables automatically get a getter function.',
    },
    {
      line: 10,
      type: 'info' as const,
      title: 'Function Modifier',
      content: 'Public functions can be called by anyone. Consider access control for sensitive functions.',
    }
  ];

  useEffect(() => {
    if (!isTyping) return;

    let index = 0;
    const interval = setInterval(() => {
      if (index < finalCode.length) {
        setCode(finalCode.slice(0, index + 1));
        index++;
      } else {
        setIsTyping(false);
        setTimeout(() => setShowEditor(true), 1000);
        clearInterval(interval);
      }
    }, 30);

    return () => clearInterval(interval);
  }, [isTyping]);

  if (showEditor) {
    return (
      <div className="space-y-4">
        <AdvancedCodeEditor
          initialCode={finalCode}
          language="solidity"
          filename="HelloWorld.sol"
          editable={true}
          copyable={true}
          showLineNumbers={true}
          annotations={annotations}
          onCodeChange={(newCode) => console.log('Code changed:', newCode)}
          onCompile={(code) => console.log('Compiling:', code)}
        />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center space-x-2 text-sm text-green-400">
            <CheckCircle className="w-4 h-4" />
            <span>Syntax highlighting active</span>
          </div>
          <div className="flex items-center space-x-2 text-sm text-blue-400">
            <Info className="w-4 h-4" />
            <span>Hover over lines for explanations</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="bg-gray-900 rounded-lg p-4 font-mono text-sm">
        <div className="flex items-center space-x-2 mb-3">
          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
          <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          <span className="text-gray-400 ml-2">HelloWorld.sol</span>
        </div>
        <pre className="text-gray-300 whitespace-pre-wrap">
          <code>{code}</code>
          {isTyping && <span className="animate-pulse">|</span>}
        </pre>
      </div>
      <div className="flex items-center space-x-2 text-sm text-gray-400">
        <CheckCircle className="w-4 h-4 text-green-400" />
        <span>AI-powered code completion and syntax highlighting</span>
      </div>
    </div>
  );
}

/**
 * AI Assistance Demo Component
 */
function AIAssistanceDemo() {
  return (
    <div className="space-y-4">
      <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
            <Brain className="w-4 h-4 text-blue-400" />
          </div>
          <div>
            <h4 className="text-blue-300 font-medium mb-2">AI Suggestion</h4>
            <p className="text-gray-300 text-sm">
              Consider adding input validation to your setMessage function. 
              You can use require() to check if the message is not empty.
            </p>
            <button className="mt-2 text-xs bg-blue-500/20 hover:bg-blue-500/30 px-2 py-1 rounded transition-colors">
              Apply Suggestion
            </button>
          </div>
        </div>
      </div>
      
      <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <div className="w-8 h-8 bg-purple-500/20 rounded-full flex items-center justify-center">
            <Sparkles className="w-4 h-4 text-purple-400" />
          </div>
          <div>
            <h4 className="text-purple-300 font-medium mb-2">Gas Optimization</h4>
            <p className="text-gray-300 text-sm">
              Your contract looks efficient! Estimated gas cost: ~150,000 for deployment.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Compilation Demo Component
 */
function CompilationDemo() {
  const sampleCode = `pragma solidity ^0.8.0;

contract HelloWorld {
    string public message;

    constructor() {
        message = "Hello, Solidity!";
    }

    function setMessage(string memory _message) public {
        message = _message;
    }
}`;

  const handleCompilationResult = (result: any) => {
    console.log('Compilation result:', result);
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h3 className="text-xl font-bold text-white mb-2">Compile & Test Your Contract</h3>
        <p className="text-gray-300">
          Experience real-time compilation with detailed feedback and security analysis
        </p>
      </div>

      <SolidityCompiler
        code={sampleCode}
        onCompilationResult={handleCompilationResult}
        autoCompile={false}
        showGasEstimation={true}
        showSecurityAnalysis={true}
      />

      <div className="bg-gradient-to-r from-green-500/10 to-blue-500/10 border border-green-500/20 rounded-lg p-4">
        <div className="flex items-center space-x-2 mb-3">
          <CheckCircle className="w-5 h-5 text-green-400" />
          <h4 className="text-green-300 font-medium">What You've Learned</h4>
        </div>
        <ul className="text-sm text-gray-300 space-y-1">
          <li>• How to compile Solidity contracts</li>
          <li>• Understanding gas estimation</li>
          <li>• Security analysis and best practices</li>
          <li>• Deployment preparation</li>
        </ul>
      </div>
    </div>
  );
}

/**
 * Collaboration Demo Component
 */
function CollaborationDemo() {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-3">
            <CheckCircle className="w-5 h-5 text-green-400" />
            <h4 className="text-green-300 font-medium">Compilation Success</h4>
          </div>
          <p className="text-gray-300 text-sm">
            Contract compiled successfully with no errors or warnings.
          </p>
        </div>
        
        <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-3">
            <Rocket className="w-5 h-5 text-purple-400" />
            <h4 className="text-purple-300 font-medium">Deploy to Testnet</h4>
          </div>
          <p className="text-gray-300 text-sm">
            Ready to deploy to Sepolia testnet. Gas estimate: 0.002 ETH
          </p>
        </div>
      </div>
      
      <div className="bg-cyan-500/10 border border-cyan-500/20 rounded-lg p-4">
        <div className="flex items-center space-x-2 mb-3">
          <Users className="w-5 h-5 text-cyan-400" />
          <h4 className="text-cyan-300 font-medium">Collaboration Active</h4>
        </div>
        <div className="flex items-center space-x-2">
          <div className="flex -space-x-2">
            <div className="w-6 h-6 bg-blue-500 rounded-full border-2 border-white"></div>
            <div className="w-6 h-6 bg-green-500 rounded-full border-2 border-white"></div>
            <div className="w-6 h-6 bg-purple-500 rounded-full border-2 border-white"></div>
          </div>
          <span className="text-gray-300 text-sm">3 developers viewing this contract</span>
        </div>
      </div>

    </div>
  );
}
