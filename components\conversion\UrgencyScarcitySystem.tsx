'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Clock, 
  Users, 
  Zap, 
  AlertCircle, 
  TrendingUp,
  Calendar,
  Target,
  Award,
  CheckCircle,
  Timer,
  Flame,
  Star
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { EnhancedButton } from '@/components/ui/EnhancedButton';

interface UrgencyTimer {
  id: string;
  type: 'countdown' | 'deadline' | 'limited_time' | 'flash_sale';
  title: string;
  description: string;
  endTime: Date;
  timezone: string;
  isActive: boolean;
  priority: 'low' | 'medium' | 'high' | 'critical';
  styling: {
    color: 'red' | 'orange' | 'yellow' | 'blue';
    animation: 'pulse' | 'glow' | 'shake' | 'bounce';
    position: 'inline' | 'floating' | 'banner' | 'modal';
  };
  conditions?: {
    userSegment?: string[];
    minTimeOnSite?: number;
    maxDisplayCount?: number;
    respectTimezone?: boolean;
  };
}

interface ScarcityIndicator {
  id: string;
  type: 'enrollment_cap' | 'limited_spots' | 'high_demand' | 'social_proof';
  title: string;
  currentValue: number;
  maxValue: number;
  updateFrequency: number; // milliseconds
  isRealTime: boolean;
  displayFormat: 'counter' | 'progress_bar' | 'percentage' | 'text';
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
  ethicalGuidelines: {
    isGenuine: boolean;
    hasMinimumThreshold: boolean;
    respectsUserChoice: boolean;
    providesValue: boolean;
  };
}

interface PersonalizedDeadline {
  id: string;
  userId: string;
  type: 'skill_assessment' | 'trial_expiry' | 'course_enrollment' | 'achievement_unlock';
  title: string;
  description: string;
  deadline: Date;
  gracePeriod?: number; // hours
  reminderSchedule: number[]; // hours before deadline
  isExtendable: boolean;
  extensionCriteria?: {
    engagementThreshold: number;
    progressThreshold: number;
    timeSpentThreshold: number;
  };
}

export function UrgencyScarcitySystem({ 
  className,
  onConversion,
  onAnalyticsUpdate 
}: { 
  className?: string;
  onConversion?: (elementId: string, action: string) => void;
  onAnalyticsUpdate?: (analytics: any) => void;
}) {
  const [activeTimers, setActiveTimers] = useState<UrgencyTimer[]>([]);
  const [scarcityIndicators, setScarcityIndicators] = useState<ScarcityIndicator[]>([]);
  const [personalizedDeadlines, setPersonalizedDeadlines] = useState<PersonalizedDeadline[]>([]);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [userTimezone, setUserTimezone] = useState('UTC');
  const [analytics, setAnalytics] = useState({
    timerViews: {} as Record<string, number>,
    conversions: {} as Record<string, number>,
    dismissals: {} as Record<string, number>,
    averageTimeToAction: {} as Record<string, number[]>
  });

  const intervalRef = useRef<NodeJS.Timeout>();
  const startTimeRef = useRef<Record<string, number>>({});

  // Initialize user timezone
  useEffect(() => {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    setUserTimezone(timezone);
  }, []);

  // Mock urgency timers
  const mockTimers: UrgencyTimer[] = [
    {
      id: 'flash-sale-timer',
      type: 'flash_sale',
      title: 'Flash Sale Ending Soon!',
      description: '50% off premium subscription - Limited time offer',
      endTime: new Date(Date.now() + 23 * 60 * 60 * 1000 + 45 * 60 * 1000 + 12 * 1000), // 23:45:12 from now
      timezone: userTimezone,
      isActive: true,
      priority: 'high',
      styling: {
        color: 'red',
        animation: 'pulse',
        position: 'banner'
      },
      conditions: {
        userSegment: ['new_visitor', 'returning_visitor'],
        minTimeOnSite: 30000, // 30 seconds
        maxDisplayCount: 3,
        respectTimezone: true
      }
    },
    {
      id: 'cohort-enrollment',
      type: 'deadline',
      title: 'Cohort Enrollment Closes',
      description: 'Join this month\'s guided learning cohort',
      endTime: new Date(Date.now() + 6 * 24 * 60 * 60 * 1000), // 6 days from now
      timezone: userTimezone,
      isActive: true,
      priority: 'medium',
      styling: {
        color: 'orange',
        animation: 'glow',
        position: 'inline'
      }
    }
  ];

  // Mock scarcity indicators
  const mockScarcityIndicators: ScarcityIndicator[] = [
    {
      id: 'enrollment-spots',
      type: 'enrollment_cap',
      title: 'Limited Spots Remaining',
      currentValue: 47,
      maxValue: 100,
      updateFrequency: 30000, // 30 seconds
      isRealTime: true,
      displayFormat: 'counter',
      urgencyLevel: 'high',
      ethicalGuidelines: {
        isGenuine: true,
        hasMinimumThreshold: true,
        respectsUserChoice: true,
        providesValue: true
      }
    },
    {
      id: 'recent-signups',
      type: 'social_proof',
      title: 'Developers Joined Recently',
      currentValue: 23,
      maxValue: 100,
      updateFrequency: 60000, // 1 minute
      isRealTime: true,
      displayFormat: 'text',
      urgencyLevel: 'medium',
      ethicalGuidelines: {
        isGenuine: true,
        hasMinimumThreshold: true,
        respectsUserChoice: true,
        providesValue: true
      }
    }
  ];

  // Initialize timers and indicators
  useEffect(() => {
    setActiveTimers(mockTimers);
    setScarcityIndicators(mockScarcityIndicators);

    // Create personalized deadline based on user behavior
    const assessmentDeadline: PersonalizedDeadline = {
      id: 'skill-assessment-deadline',
      userId: 'current-user',
      type: 'skill_assessment',
      title: 'Complete Your Skill Assessment',
      description: 'Your personalized learning path expires in 2 days',
      deadline: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
      gracePeriod: 24, // 24 hours grace period
      reminderSchedule: [48, 24, 12, 6, 1], // hours before deadline
      isExtendable: true,
      extensionCriteria: {
        engagementThreshold: 70,
        progressThreshold: 25,
        timeSpentThreshold: 1800000 // 30 minutes
      }
    };

    setPersonalizedDeadlines([assessmentDeadline]);
  }, [userTimezone]);

  // Update current time every second
  useEffect(() => {
    intervalRef.current = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Update scarcity indicators periodically
  useEffect(() => {
    const updateScarcityValues = () => {
      setScarcityIndicators(prev => prev.map(indicator => {
        if (!indicator.isRealTime) return indicator;

        let newValue = indicator.currentValue;
        
        // Simulate realistic changes
        if (indicator.type === 'enrollment_cap') {
          // Slowly decrease available spots
          if (Math.random() < 0.3) { // 30% chance to decrease
            newValue = Math.max(1, newValue - 1);
          }
        } else if (indicator.type === 'social_proof') {
          // Occasionally increase recent signups
          if (Math.random() < 0.2) { // 20% chance to increase
            newValue = Math.min(indicator.maxValue, newValue + Math.floor(Math.random() * 3) + 1);
          }
        }

        return { ...indicator, currentValue: newValue };
      }));
    };

    const scarcityInterval = setInterval(updateScarcityValues, 30000); // Update every 30 seconds
    return () => clearInterval(scarcityInterval);
  }, []);

  // Format time remaining
  const formatTimeRemaining = useCallback((endTime: Date) => {
    const now = currentTime.getTime();
    const end = endTime.getTime();
    const diff = Math.max(0, end - now);

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

    if (days > 0) {
      return { days, hours, minutes, seconds, isExpired: false };
    } else if (hours > 0) {
      return { days: 0, hours, minutes, seconds, isExpired: false };
    } else if (minutes > 0) {
      return { days: 0, hours: 0, minutes, seconds, isExpired: false };
    } else if (seconds > 0) {
      return { days: 0, hours: 0, minutes: 0, seconds, isExpired: false };
    } else {
      return { days: 0, hours: 0, minutes: 0, seconds: 0, isExpired: true };
    }
  }, [currentTime]);

  // Handle timer interaction
  const handleTimerAction = useCallback((timerId: string, action: string) => {
    const startTime = startTimeRef.current[timerId];
    const timeToAction = startTime ? Date.now() - startTime : 0;

    setAnalytics(prev => ({
      ...prev,
      conversions: {
        ...prev.conversions,
        [timerId]: (prev.conversions[timerId] || 0) + 1
      },
      averageTimeToAction: {
        ...prev.averageTimeToAction,
        [timerId]: [...(prev.averageTimeToAction[timerId] || []), timeToAction]
      }
    }));

    onConversion?.(timerId, action);

    // Track with external analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'urgency_timer_conversion', {
        timer_id: timerId,
        action,
        time_to_action: timeToAction,
        timestamp: new Date().toISOString()
      });
    }
  }, [onConversion]);

  // Handle timer dismissal
  const handleTimerDismiss = useCallback((timerId: string) => {
    setAnalytics(prev => ({
      ...prev,
      dismissals: {
        ...prev.dismissals,
        [timerId]: (prev.dismissals[timerId] || 0) + 1
      }
    }));

    setActiveTimers(prev => prev.filter(timer => timer.id !== timerId));
  }, []);

  // Track timer view
  const trackTimerView = useCallback((timerId: string) => {
    if (!startTimeRef.current[timerId]) {
      startTimeRef.current[timerId] = Date.now();
      
      setAnalytics(prev => ({
        ...prev,
        timerViews: {
          ...prev.timerViews,
          [timerId]: (prev.timerViews[timerId] || 0) + 1
        }
      }));
    }
  }, []);

  // Get urgency color
  const getUrgencyColor = (color: string, urgencyLevel: string) => {
    const baseColors = {
      red: 'from-red-600 to-red-700 border-red-500',
      orange: 'from-orange-600 to-orange-700 border-orange-500',
      yellow: 'from-yellow-600 to-yellow-700 border-yellow-500',
      blue: 'from-blue-600 to-blue-700 border-blue-500'
    };

    const urgencyColors = {
      critical: 'from-red-600 to-red-800 border-red-400',
      high: 'from-orange-600 to-red-600 border-orange-400',
      medium: 'from-yellow-600 to-orange-600 border-yellow-400',
      low: 'from-blue-600 to-blue-700 border-blue-400'
    };

    return urgencyLevel === 'critical' || urgencyLevel === 'high' 
      ? urgencyColors[urgencyLevel as keyof typeof urgencyColors]
      : baseColors[color as keyof typeof baseColors];
  };

  // Get animation classes
  const getAnimationClasses = (animation: string) => {
    const animations = {
      pulse: 'animate-pulse',
      glow: 'animate-pulse shadow-lg',
      shake: 'animate-bounce',
      bounce: 'animate-bounce'
    };

    return animations[animation as keyof typeof animations] || '';
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Urgency Timers */}
      <AnimatePresence>
        {activeTimers.map(timer => {
          const timeRemaining = formatTimeRemaining(timer.endTime);
          
          if (timeRemaining.isExpired) return null;

          // Track view when timer becomes visible
          React.useEffect(() => {
            trackTimerView(timer.id);
          }, [timer.id, trackTimerView]);

          return (
            <motion.div
              key={timer.id}
              className={cn(
                'relative overflow-hidden rounded-lg border-2 shadow-lg',
                getUrgencyColor(timer.styling.color, timer.priority),
                getAnimationClasses(timer.styling.animation),
                timer.styling.position === 'banner' ? 'w-full' : 'max-w-md',
                timer.styling.position === 'floating' ? 'fixed bottom-6 right-6 z-50' : ''
              )}
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: -20 }}
              transition={{ duration: 0.5, type: "spring", stiffness: 300, damping: 30 }}
            >
              {/* Background Pattern */}
              <div className="absolute inset-0 bg-gradient-to-r opacity-10">
                <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.1%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
              </div>

              <div className="relative p-6">
                {/* Close Button */}
                <button
                  onClick={() => handleTimerDismiss(timer.id)}
                  className="absolute top-2 right-2 p-1 text-white/60 hover:text-white/80 transition-colors"
                  aria-label="Dismiss timer"
                >
                  ×
                </button>

                {/* Timer Content */}
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="p-3 bg-white/20 rounded-full">
                      <Clock className="w-6 h-6 text-white" />
                    </div>
                  </div>
                  
                  <div className="flex-1">
                    <h3 className="font-bold text-white text-lg mb-1">{timer.title}</h3>
                    <p className="text-white/90 text-sm mb-4">{timer.description}</p>
                    
                    {/* Countdown Display */}
                    <div className="grid grid-cols-4 gap-2 mb-4">
                      {timeRemaining.days > 0 && (
                        <div className="text-center">
                          <div className="text-2xl font-bold text-white">{timeRemaining.days}</div>
                          <div className="text-xs text-white/70">DAYS</div>
                        </div>
                      )}
                      <div className="text-center">
                        <div className="text-2xl font-bold text-white">{timeRemaining.hours.toString().padStart(2, '0')}</div>
                        <div className="text-xs text-white/70">HOURS</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-white">{timeRemaining.minutes.toString().padStart(2, '0')}</div>
                        <div className="text-xs text-white/70">MINS</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-white">{timeRemaining.seconds.toString().padStart(2, '0')}</div>
                        <div className="text-xs text-white/70">SECS</div>
                      </div>
                    </div>

                    {/* Action Button */}
                    <EnhancedButton
                      onClick={() => handleTimerAction(timer.id, 'claim_offer')}
                      className="w-full bg-white text-gray-900 hover:bg-gray-100 font-semibold"
                      touchTarget
                    >
                      <Zap className="w-4 h-4 mr-2" />
                      Claim Offer Now
                    </EnhancedButton>
                  </div>
                </div>
              </div>
            </motion.div>
          );
        })}
      </AnimatePresence>

      {/* Scarcity Indicators */}
      <div className="space-y-3">
        {scarcityIndicators.map(indicator => (
          <motion.div
            key={indicator.id}
            className={cn(
              'bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-4',
              indicator.urgencyLevel === 'critical' && 'border-red-500/50 bg-red-500/10',
              indicator.urgencyLevel === 'high' && 'border-orange-500/50 bg-orange-500/10'
            )}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={cn(
                  'p-2 rounded-lg',
                  indicator.urgencyLevel === 'critical' ? 'bg-red-500/20' :
                  indicator.urgencyLevel === 'high' ? 'bg-orange-500/20' :
                  'bg-blue-500/20'
                )}>
                  {indicator.type === 'enrollment_cap' && <Users className="w-4 h-4 text-white" />}
                  {indicator.type === 'social_proof' && <TrendingUp className="w-4 h-4 text-white" />}
                  {indicator.type === 'high_demand' && <Flame className="w-4 h-4 text-white" />}
                </div>
                <div>
                  <h4 className="font-medium text-white">{indicator.title}</h4>
                  <div className="text-sm text-gray-300">
                    {indicator.displayFormat === 'counter' && (
                      <span>Only <strong className="text-orange-400">{indicator.currentValue}</strong> spots left</span>
                    )}
                    {indicator.displayFormat === 'text' && (
                      <span><strong className="text-green-400">{indicator.currentValue}</strong> developers joined in the last hour</span>
                    )}
                    {indicator.displayFormat === 'percentage' && (
                      <span>{Math.round((indicator.currentValue / indicator.maxValue) * 100)}% capacity</span>
                    )}
                  </div>
                </div>
              </div>
              
              {indicator.displayFormat === 'progress_bar' && (
                <div className="w-24 h-2 bg-gray-700 rounded-full overflow-hidden">
                  <motion.div
                    className={cn(
                      'h-full rounded-full',
                      indicator.urgencyLevel === 'critical' ? 'bg-red-500' :
                      indicator.urgencyLevel === 'high' ? 'bg-orange-500' :
                      'bg-blue-500'
                    )}
                    initial={{ width: 0 }}
                    animate={{ width: `${(indicator.currentValue / indicator.maxValue) * 100}%` }}
                    transition={{ duration: 0.5 }}
                  />
                </div>
              )}
            </div>
          </motion.div>
        ))}
      </div>

      {/* Personalized Deadlines */}
      <div className="space-y-3">
        {personalizedDeadlines.map(deadline => {
          const timeRemaining = formatTimeRemaining(deadline.deadline);
          
          if (timeRemaining.isExpired) return null;

          return (
            <motion.div
              key={deadline.id}
              className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 rounded-lg border border-purple-500/30 p-4"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <div className="p-2 bg-purple-500/20 rounded-lg">
                    <Target className="w-5 h-5 text-purple-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-white mb-1">{deadline.title}</h4>
                    <p className="text-gray-300 text-sm mb-2">{deadline.description}</p>
                    <div className="text-sm text-purple-300">
                      Expires in {timeRemaining.days > 0 ? `${timeRemaining.days}d ` : ''}
                      {timeRemaining.hours}h {timeRemaining.minutes}m
                    </div>
                  </div>
                </div>
                
                <EnhancedButton
                  onClick={() => handleTimerAction(deadline.id, 'complete_assessment')}
                  size="sm"
                  className="bg-purple-600 hover:bg-purple-700 text-white"
                >
                  Complete Now
                </EnhancedButton>
              </div>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
}
