#!/usr/bin/env node

/**
 * Quick TypeScript Error Fixing Script
 * Fixes the most common TypeScript errors preventing compilation
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkAndCreateMissingFiles() {
  log('🔍 Checking for missing critical files...', 'cyan');
  
  const criticalFiles = [
    {
      path: 'lib/utils/index.ts',
      content: `// Utils barrel export
export * from '../utils';
export * from './accessibility';
`
    },
    {
      path: 'components/ui/toaster.tsx',
      content: `'use client';

import React from 'react';

export function Toaster() {
  return <div id="toast-container" />;
}
`
    },
    {
      path: 'components/errors/ErrorBoundary.tsx',
      content: `'use client';

import React, { Component, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
}

export class PageErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): State {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Error boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <div>Something went wrong.</div>;
    }

    return this.props.children;
  }
}
`
    },
    {
      path: 'components/ui/Accessibility.tsx',
      content: `'use client';

import React from 'react';

export function SkipLink() {
  return (
    <a 
      href="#main-content" 
      className="sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0 bg-blue-600 text-white p-2 z-50"
    >
      Skip to main content
    </a>
  );
}
`
    }
  ];

  criticalFiles.forEach(file => {
    const fullPath = path.join(process.cwd(), file.path);
    const dir = path.dirname(fullPath);
    
    if (!fs.existsSync(fullPath)) {
      log(`📝 Creating missing file: ${file.path}`, 'yellow');
      
      // Create directory if it doesn't exist
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      
      fs.writeFileSync(fullPath, file.content);
      log(`✅ Created: ${file.path}`, 'green');
    }
  });
}

function fixCommonSyntaxErrors() {
  log('🔧 Fixing common syntax errors...', 'cyan');
  
  const filesToFix = [
    'app/api/errors/route.ts',
    'lib/git/CommitManager.ts',
    'lib/errors/ErrorContext.tsx',
    'lib/config/secrets.ts',
    'lib/performance/PerformanceMonitor.ts',
    'lib/security/headers.ts',
    'lib/security/session.ts'
  ];

  filesToFix.forEach(filePath => {
    const fullPath = path.join(process.cwd(), filePath);
    if (fs.existsSync(fullPath)) {
      try {
        let content = fs.readFileSync(fullPath, 'utf8');
        let modified = false;

        // Fix common template literal issues
        if (content.includes('}`);') && !content.includes('console.log(`')) {
          content = content.replace(/}`\);/g, '`);');
          modified = true;
        }

        // Fix broken console.log statements
        if (content.includes('+ \'...\',')) {
          content = content.replace(/\+ '\.\.\.'/g, '.substring(0, 8) + \'...\'');
          modified = true;
        }

        if (modified) {
          fs.writeFileSync(fullPath, content);
          log(`✅ Fixed syntax in: ${filePath}`, 'green');
        }
      } catch (error) {
        log(`❌ Error fixing ${filePath}: ${error.message}`, 'red');
      }
    }
  });
}

function runQuickTypeCheck() {
  log('📊 Running quick type check...', 'cyan');
  
  try {
    execSync('npx tsc --noEmit --skipLibCheck --maxNodeModuleJsDepth 0', { 
      stdio: 'pipe',
      timeout: 30000 
    });
    log('✅ Type check passed!', 'green');
    return true;
  } catch (error) {
    const output = error.stdout?.toString() || error.stderr?.toString() || '';
    const errorLines = output.split('\n').filter(line => line.includes('error')).slice(0, 10);
    
    if (errorLines.length > 0) {
      log('❌ Type errors found:', 'red');
      errorLines.forEach(line => log(`  ${line}`, 'yellow'));
    }
    return false;
  }
}

async function main() {
  log('🚀 Quick TypeScript Error Fixing Script', 'cyan');
  log('=====================================\n', 'cyan');

  // Step 1: Check and create missing files
  checkAndCreateMissingFiles();

  // Step 2: Fix common syntax errors
  fixCommonSyntaxErrors();

  // Step 3: Run type check
  const success = runQuickTypeCheck();

  if (success) {
    log('\n🎉 All critical errors fixed! You can now run: npm run build', 'green');
  } else {
    log('\n⚠️  Some errors remain. Manual fixes may be required.', 'yellow');
  }
}

main().catch(console.error);
