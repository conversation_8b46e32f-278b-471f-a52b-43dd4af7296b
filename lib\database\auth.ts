/**
 * Production Database Service for Authentication
 * Handles user authentication, registration, and profile management
 */

import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { z } from 'zod';

// Environment variables validation
const envSchema = z.object({
  DATABASE_URL: z.string().url(),
  JWT_SECRET: z.string().min(32),
  JWT_EXPIRES_IN: z.string().default('7d'),
  BCRYPT_ROUNDS: z.string().transform(Number).default('12'),
});

const env = envSchema.parse({
  DATABASE_URL: process.env.DATABASE_URL || 'postgresql://localhost:5432/solidity_learn',
  JWT_SECRET: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
  JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '7d',
  BCRYPT_ROUNDS: process.env.BCRYPT_ROUNDS || '12',
});

// User types
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'STUDENT' | 'MENTOR' | 'INSTRUCTOR' | 'ADMIN';
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  emailVerified: boolean;
  profile: {
    avatar?: string;
    bio?: string;
    location?: string;
    website?: string;
    github?: string;
    twitter?: string;
    linkedin?: string;
    xpTotal: number;
    level: number;
    lessonsCompleted: number;
    coursesCompleted: number;
    achievementsCount: number;
    currentStreak: number;
    longestStreak: number;
  };
  preferences: {
    theme: 'light' | 'dark';
    language: string;
    timezone: string;
    emailNotifications: boolean;
    pushNotifications: boolean;
    weeklyDigest: boolean;
    achievementNotifications: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
}

export interface CreateUserData {
  email: string;
  password: string;
  name: string;
  role?: 'STUDENT' | 'MENTOR' | 'INSTRUCTOR' | 'ADMIN';
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthResult {
  user: Omit<User, 'password'>;
  token: string;
}

// Database connection (replace with your actual database client)
// This is a placeholder - implement with your chosen database (PostgreSQL, MySQL, etc.)
class DatabaseClient {
  async query<T = any>(sql: string, params?: any[]): Promise<T[]> {
    // TODO: Implement actual database query
    // Example with PostgreSQL:
    // const client = await pool.connect();
    // try {
    //   const result = await client.query(sql, params);
    //   return result.rows;
    // } finally {
    //   client.release();
    // }
    
    // For now, throw an error to indicate this needs implementation
    throw new Error('Database connection not implemented. Please configure your database client.');
  }

  async queryOne<T = any>(sql: string, params?: any[]): Promise<T | null> {
    const results = await this.query<T>(sql, params);
    return results[0] || null;
  }
}

const db = new DatabaseClient();

// Authentication service
export class AuthService {
  /**
   * Hash a password using bcrypt
   */
  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, env.BCRYPT_ROUNDS);
  }

  /**
   * Verify a password against a hash
   */
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  /**
   * Generate a JWT token for a user
   */
  static generateToken(userId: string): string {
    return jwt.sign({ userId }, env.JWT_SECRET, {
      expiresIn: env.JWT_EXPIRES_IN,
    });
  }

  /**
   * Verify and decode a JWT token
   */
  static verifyToken(token: string): { userId: string } {
    try {
      return jwt.verify(token, env.JWT_SECRET) as { userId: string };
    } catch (error) {
      throw new Error('Invalid or expired token');
    }
  }

  /**
   * Create a new user account
   */
  static async createUser(userData: CreateUserData): Promise<AuthResult> {
    const { email, password, name, role = 'STUDENT' } = userData;

    // Check if user already exists
    const existingUser = await db.queryOne(
      'SELECT id FROM users WHERE email = $1',
      [email.toLowerCase()]
    );

    if (existingUser) {
      throw new Error('User with this email already exists');
    }

    // Hash password
    const hashedPassword = await this.hashPassword(password);

    // Create user
    const userId = crypto.randomUUID();
    const now = new Date();

    await db.query(`
      INSERT INTO users (
        id, email, password_hash, name, role, status, email_verified,
        xp_total, level, lessons_completed, courses_completed, 
        achievements_count, current_streak, longest_streak,
        theme, language, timezone, email_notifications, 
        push_notifications, weekly_digest, achievement_notifications,
        created_at, updated_at
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14,
        $15, $16, $17, $18, $19, $20, $21, $22, $23
      )
    `, [
      userId, email.toLowerCase(), hashedPassword, name, role, 'ACTIVE', false,
      0, 1, 0, 0, 0, 0, 0, // Profile defaults
      'dark', 'en', 'UTC', true, true, true, true, // Preference defaults
      now, now
    ]);

    // Fetch the created user
    const user = await this.getUserById(userId);
    if (!user) {
      throw new Error('Failed to create user');
    }

    // Generate token
    const token = this.generateToken(userId);

    return { user, token };
  }

  /**
   * Authenticate a user with email and password
   */
  static async login(credentials: LoginCredentials): Promise<AuthResult> {
    const { email, password } = credentials;

    // Find user by email
    const userRecord = await db.queryOne(`
      SELECT id, email, password_hash, name, role, status, email_verified,
             avatar, bio, location, website, github, twitter, linkedin,
             xp_total, level, lessons_completed, courses_completed,
             achievements_count, current_streak, longest_streak,
             theme, language, timezone, email_notifications,
             push_notifications, weekly_digest, achievement_notifications,
             created_at, updated_at, last_login_at
      FROM users WHERE email = $1
    `, [email.toLowerCase()]);

    if (!userRecord) {
      throw new Error('Invalid email or password');
    }

    // Verify password
    const isValidPassword = await this.verifyPassword(password, userRecord.password_hash);
    if (!isValidPassword) {
      throw new Error('Invalid email or password');
    }

    // Check if user is active
    if (userRecord.status !== 'ACTIVE') {
      throw new Error('Account is inactive or suspended');
    }

    // Update last login
    await db.query(
      'UPDATE users SET last_login_at = $1, updated_at = $1 WHERE id = $2',
      [new Date(), userRecord.id]
    );

    // Convert database record to User object
    const user = this.mapDatabaseRecordToUser(userRecord);

    // Generate token
    const token = this.generateToken(userRecord.id);

    return { user, token };
  }

  /**
   * Get user by ID
   */
  static async getUserById(userId: string): Promise<Omit<User, 'password'> | null> {
    const userRecord = await db.queryOne(`
      SELECT id, email, name, role, status, email_verified,
             avatar, bio, location, website, github, twitter, linkedin,
             xp_total, level, lessons_completed, courses_completed,
             achievements_count, current_streak, longest_streak,
             theme, language, timezone, email_notifications,
             push_notifications, weekly_digest, achievement_notifications,
             created_at, updated_at, last_login_at
      FROM users WHERE id = $1
    `, [userId]);

    if (!userRecord) {
      return null;
    }

    return this.mapDatabaseRecordToUser(userRecord);
  }

  /**
   * Update user profile
   */
  static async updateProfile(
    userId: string, 
    updates: Partial<User['profile']>
  ): Promise<Omit<User, 'password'>> {
    const setClause = [];
    const values = [];
    let paramIndex = 1;

    // Build dynamic update query
    for (const [key, value] of Object.entries(updates)) {
      setClause.push(`${key} = $${paramIndex}`);
      values.push(value);
      paramIndex++;
    }

    if (setClause.length === 0) {
      throw new Error('No updates provided');
    }

    // Add updated_at
    setClause.push(`updated_at = $${paramIndex}`);
    values.push(new Date());
    paramIndex++;

    // Add user ID for WHERE clause
    values.push(userId);

    await db.query(`
      UPDATE users 
      SET ${setClause.join(', ')}
      WHERE id = $${paramIndex}
    `, values);

    const updatedUser = await this.getUserById(userId);
    if (!updatedUser) {
      throw new Error('User not found after update');
    }

    return updatedUser;
  }

  /**
   * Map database record to User object
   */
  private static mapDatabaseRecordToUser(record: any): Omit<User, 'password'> {
    return {
      id: record.id,
      email: record.email,
      name: record.name,
      role: record.role,
      status: record.status,
      emailVerified: record.email_verified,
      profile: {
        avatar: record.avatar,
        bio: record.bio,
        location: record.location,
        website: record.website,
        github: record.github,
        twitter: record.twitter,
        linkedin: record.linkedin,
        xpTotal: record.xp_total,
        level: record.level,
        lessonsCompleted: record.lessons_completed,
        coursesCompleted: record.courses_completed,
        achievementsCount: record.achievements_count,
        currentStreak: record.current_streak,
        longestStreak: record.longest_streak,
      },
      preferences: {
        theme: record.theme,
        language: record.language,
        timezone: record.timezone,
        emailNotifications: record.email_notifications,
        pushNotifications: record.push_notifications,
        weeklyDigest: record.weekly_digest,
        achievementNotifications: record.achievement_notifications,
      },
      createdAt: new Date(record.created_at),
      updatedAt: new Date(record.updated_at),
      lastLoginAt: record.last_login_at ? new Date(record.last_login_at) : undefined,
    };
  }
}
